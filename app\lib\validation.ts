import { UniverAPI } from '@/types/univer'

// 验证规则接口定义
interface ValidationRule {
  type: string
  cell?: string
  expectedValue?: unknown
  expectedFormula?: string
  expectedFormat?: string
  expectedStyle?: Record<string, unknown>
  dataRange?: string
  expectedType?: string
  expectedFields?: string[]
  // 透视表严格验证相关字段
  strictValidation?: boolean
  expectedRowHeaders?: string[]
  expectedColumnHeaders?: string[]
  expectedTotalValue?: number
  // 筛选验证相关字段
  expectedVisibleRows?: number
  expectedFilteredData?: Array<Record<string, unknown>>
  // 排序验证相关字段
  expectedOrder?: string[] | number[]
  sortColumn?: string
  sortDirection?: 'asc' | 'desc'
  // 条件格式验证相关字段
  expectedBackgroundColor?: string
  expectedTextColor?: string
  conditionRange?: string
}

// 验证结果接口
interface ValidationResult {
  success: boolean
  message: string
  details?: Record<string, unknown>
}

/**
 * Excel任务验证服务
 * 基于Univer Facade API实现各种验证功能
 */
export class ExcelValidationService {
  private univerAPI: UniverAPI

  constructor(univerAPI: UniverAPI) {
    this.univerAPI = univerAPI
  }

  /**
   * 验证任务是否完成
   * @param validationRule 验证规则
   * @returns 验证结果
   */
  async validateTask(validationRule: ValidationRule): Promise<ValidationResult> {
    try {
      switch (validationRule.type) {
        case 'cellValue':
        case 'input': // input类型等同于cellValue验证
          return await this.validateCellValue(validationRule)
        case 'cellFormula':
          return await this.validateCellFormula(validationRule)
        case 'cellFormat':
          return await this.validateCellFormat(validationRule)
        case 'cellStyle':
          return await this.validateCellStyle(validationRule)
        case 'chart':
          return await this.validateChart(validationRule)
        case 'pivotTable':
          return await this.validatePivotTable(validationRule)
        case 'filter':
          return await this.validateFilter(validationRule)
        case 'sort':
          return await this.validateSort(validationRule)
        case 'conditionalFormat':
          return await this.validateConditionalFormat(validationRule)
        default:
          return {
            success: false,
            message: `未知的验证类型: ${validationRule.type}`
          }
      }
    } catch (error) {
      console.error('验证过程中发生错误:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试',
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格值
   */
  private async validateCellValue(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || rule.expectedValue === undefined) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望值'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取活动工作簿'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取活动工作表'
        }
      }

      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      const cellValue = range.getValue()
      const actualValue = cellValue?.v || cellValue // 处理Univer的值格式
      
      // 类型转换和比较
      const expectedValue = rule.expectedValue
      const isMatch = this.compareValues(actualValue, expectedValue)

      return {
        success: isMatch,
        message: isMatch 
          ? '单元格值验证通过！' 
          : `单元格 ${rule.cell} 的值不正确。期望: "${expectedValue}"，实际: "${actualValue}"`,
        details: {
          cell: rule.cell,
          expected: expectedValue,
          actual: actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格值时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格公式
   */
  private async validateCellFormula(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedFormula) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望公式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.cell)
      
      // 获取单元格公式
      const cellData = range.getCellData()
      const actualFormula = cellData?.f || ''
      
      // 标准化公式格式（去除空格，统一大小写）
      const normalizeFormula = (formula: string) => {
        return formula.replace(/\s+/g, '').toUpperCase()
      }
      
      const expectedNormalized = normalizeFormula(rule.expectedFormula)
      const actualNormalized = normalizeFormula(actualFormula)
      
      const isFormulaMatch = actualNormalized === expectedNormalized
      
      // 如果有期望值，也验证计算结果
      let isValueMatch = true
      let actualValue = null
      if (rule.expectedValue !== undefined) {
        const cellValue = range.getValue()
        actualValue = cellValue?.v || cellValue
        isValueMatch = this.compareValues(actualValue, rule.expectedValue)
      }
      
      const success = isFormulaMatch && isValueMatch
      
      let message = ''
      if (!isFormulaMatch) {
        message = `公式不正确。期望: "${rule.expectedFormula}"，实际: "${actualFormula}"`
      } else if (!isValueMatch) {
        message = `公式正确但计算结果不对。期望结果: ${rule.expectedValue}，实际结果: ${actualValue}`
      } else {
        message = '公式验证通过！'
      }
      
      return {
        success,
        message,
        details: {
          cell: rule.cell,
          expectedFormula: rule.expectedFormula,
          actualFormula,
          expectedValue: rule.expectedValue,
          actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证公式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格格式
   */
  private async validateCellFormat(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedFormat) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望格式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.cell)
      
      // 获取单元格格式信息
      const cellData = range.getCellData()
      const numberFormat = range.getNumberFormat() || cellData?.s?.n?.pattern || ''
      
      // 获取单元格的显示值
      const cellValue = range.getValue()
      const displayValue = cellValue?.v !== undefined ? cellValue.v.toString() : cellValue?.toString() || ''
      
      // 根据期望格式类型进行验证
      let isFormatMatch = false
      let formatDescription = ''
      
      switch (rule.expectedFormat) {
        case 'currency':
          // 检查格式字符串或显示值是否包含货币符号
          isFormatMatch = this.isCurrencyFormat(numberFormat) || this.isCurrencyFormat(displayValue)
          formatDescription = '货币格式'
          break
        case 'percentage':
          isFormatMatch = this.isPercentageFormat(numberFormat) || this.isPercentageFormat(displayValue)
          formatDescription = '百分比格式'
          break
        case 'date':
          isFormatMatch = this.isDateFormat(numberFormat) || this.isDateFormat(displayValue)
          formatDescription = '日期格式'
          break
        default:
          isFormatMatch = numberFormat === rule.expectedFormat
          formatDescription = rule.expectedFormat
      }
      
      return {
        success: isFormatMatch,
        message: isFormatMatch 
          ? `${formatDescription}验证通过！`
          : `单元格 ${rule.cell} 的格式不正确。期望: ${formatDescription}，实际格式: "${numberFormat}"，显示值: "${displayValue}"`,
        details: {
          cell: rule.cell,
          expectedFormat: rule.expectedFormat,
          actualFormat: numberFormat,
          displayValue: displayValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证格式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格样式
   */
  private async validateCellStyle(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedStyle) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望样式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.cell)
      
      // 获取单元格样式信息
      const style = range.getCellStyleData() || {}
      
      const validationResults = []
      
      // 验证粗体
      if (rule.expectedStyle.bold !== undefined) {
        // 只检查标准的粗体属性，避免过于宽松的验证
        const isBold = style.bl === 1 || style.bl === true || 
                      style.bold === 1 || style.bold === true ||
                      style.fontWeight === 'bold' || style.fontWeight === 700 ||
                      (style.ft && (style.ft.bl === 1 || style.ft.bl === true))
        const expectedBold = rule.expectedStyle.bold
        
        console.log('粗体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isBold: isBold,
          expectedBold: expectedBold,
          styleKeys: Object.keys(style),
          styleType: typeof style
        })
        
        validationResults.push({
          property: 'bold',
          expected: expectedBold,
          actual: isBold,
          match: isBold === expectedBold
        })
      }
      
      // 验证斜体
      if (rule.expectedStyle.italic !== undefined) {
        // 支持多种斜体属性格式
        const isItalic = style.it === 1 || style.it === true ||
                         style.italic === 1 || style.italic === true ||
                         style.fontStyle === 'italic' ||
                         (style.ft && (style.ft.it === 1 || style.ft.it === true))
        const expectedItalic = rule.expectedStyle.italic
        
        console.log('斜体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isItalic: isItalic,
          expectedItalic: expectedItalic,
          styleKeys: Object.keys(style)
        })
        
        validationResults.push({
          property: 'italic',
          expected: expectedItalic,
          actual: isItalic,
          match: isItalic === expectedItalic
        })
      }
      
      // 验证字体颜色
      if (rule.expectedStyle.color) {
        const actualColor = style.cl?.rgb || ''
        const expectedColor = rule.expectedStyle.color
        validationResults.push({
          property: 'color',
          expected: expectedColor,
          actual: actualColor,
          match: actualColor === expectedColor
        })
      }
      
      // 验证背景色
      if (rule.expectedStyle.backgroundColor) {
        const actualBgColor = style.bg?.rgb || ''
        const expectedBgColor = rule.expectedStyle.backgroundColor
        validationResults.push({
          property: 'backgroundColor',
          expected: expectedBgColor,
          actual: actualBgColor,
          match: actualBgColor === expectedBgColor
        })
      }
      
      const allMatch = validationResults.every(result => result.match)
      const failedValidations = validationResults.filter(result => !result.match)
      
      let message = ''
      if (allMatch) {
        message = '样式验证通过！'
      } else {
        const failedDetails = failedValidations.map(v => 
          `${v.property}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `单元格 ${rule.cell} 的样式不正确。失败的属性: ${failedDetails}。当前样式对象: ${JSON.stringify(style)}`
      }
      
      return {
        success: allMatch,
        message,
        details: {
          cell: rule.cell,
          validationResults,
          actualStyle: style
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证样式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证图表
   */
  private async validateChart(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 简化的图表验证逻辑
      // 检查是否选择了正确的数据范围
      if (rule.dataRange) {
        const workbook = this.univerAPI.getActiveWorkbook()
        const worksheet = workbook.getActiveSheet()
        
        // 验证数据范围是否有数据
        const range = worksheet.getRange(rule.dataRange)
        const values = range.getValues()
        
        if (!values || values.length === 0) {
          return {
            success: false,
            message: `数据范围 ${rule.dataRange} 中没有数据。请确保数据范围正确。`,
            details: {
              dataRange: rule.dataRange,
              expectedType: rule.expectedType
            }
          }
        }
      }
      
      // 检查DOM中是否存在图表相关元素
      const chartSelectors = [
        '.univer-chart',
        '.echarts-chart', 
        '.chart-container',
        '[data-chart-id]',
        'canvas[data-zr-dom-id]', // ECharts canvas
        '.univer-drawing-object' // Univer绘图对象
      ]
      
      let chartFound = false
      for (const selector of chartSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          chartFound = true
          console.log(`找到图表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }
      
      if (!chartFound) {
        return {
          success: false,
          message: '未找到图表。请按照操作步骤创建图表：\n1. 选择数据范围\n2. 点击"插入"选项卡\n3. 选择"图表"\n4. 选择合适的图表类型',
          details: {
            expectedType: rule.expectedType,
            dataRange: rule.dataRange,
            hint: '请确保已经插入了图表'
          }
        }
      }
      
      return {
        success: true,
        message: '图表创建成功！任务完成。',
        details: {
          expectedType: rule.expectedType,
          dataRange: rule.dataRange,
          chartFound: true
        }
      }
      
    } catch (error) {
      console.error('图表验证错误:', error)
      return {
        success: false,
        message: `图表验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证数据透视表
   */
  private async validatePivotTable(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 更严格的透视表验证逻辑
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      
      // 1. 检查是否创建了新的工作表（透视表通常会创建新工作表）
      let hasNewWorksheet = false
      try {
        // 尝试获取工作表数量，如果有多个工作表说明可能创建了透视表
        const worksheetCount = workbook.getSheetCount ? workbook.getSheetCount() : 1
        if (worksheetCount > 1) {
          hasNewWorksheet = true
          console.log(`检测到 ${worksheetCount} 个工作表，可能包含透视表`)
        }
      } catch (e) {
        console.log('检查工作表数量失败:', e)
      }
      
      // 2. 检查DOM中是否存在透视表相关元素
      const pivotSelectors = [
        '.pivot-table',
        '.univer-pivot-table', 
        '.pivot-container',
        '[data-pivot-id]',
        '.univer-pivot',
        '.pivot-field-list',
        '.univer-drawing-object', // Univer绘图对象可能包含透视表
        '.pivot-table-container',
        '.ms-pivot-table', // 可能的Microsoft样式
        '[class*="pivot"]' // 包含pivot的类名
      ]
      
      let pivotElementFound = false
      let foundSelector = ''
      for (const selector of pivotSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          pivotElementFound = true
          foundSelector = selector
          console.log(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }
      
      // 3. 检查当前工作表中是否有透视表的特征数据结构
      let hasPivotStructure = false
      try {
        // 检查是否有类似透视表的数据结构（行标题、列标题、汇总数据）
        const range = worksheet.getRange('A1:Z50') // 检查较大范围
        const values = range.getValues()
        
        if (values && values.length > 0) {
          // 查找可能的透视表结构特征
          for (let i = 0; i < Math.min(values.length, 20); i++) {
            for (let j = 0; j < Math.min(values[i].length, 20); j++) {
              const cellValue = values[i][j]
              if (cellValue && typeof cellValue === 'string') {
                // 检查是否包含透视表常见的标识词
                const pivotKeywords = ['总计', '小计', '求和', '计数', '平均值', '最大值', '最小值', 'Sum', 'Count', 'Average', 'Total']
                if (pivotKeywords.some(keyword => cellValue.includes(keyword))) {
                  hasPivotStructure = true
                  console.log(`在 ${String.fromCharCode(65 + j)}${i + 1} 找到透视表特征: ${cellValue}`)
                  break
                }
              }
            }
            if (hasPivotStructure) break
          }
        }
      } catch (e) {
        console.log('检查透视表数据结构失败:', e)
      }
      
      // 4. 验证期望的字段是否存在（如果配置了expectedFields）
      const fieldsValidation = { found: true, details: '' }
      const strictValidation = { passed: true, details: '' }
      
      if (rule.expectedFields && rule.expectedFields.length > 0) {
        try {
          const range = worksheet.getRange('A1:Z50')
          const values = range.getValues()
          const foundFields: string[] = []
          
          // 在工作表中查找期望的字段
          for (const expectedField of rule.expectedFields) {
            let fieldFound = false
            for (let i = 0; i < Math.min(values.length, 20); i++) {
              for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                const cellValue = values[i][j]
                if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedField)) {
                  fieldFound = true
                  foundFields.push(expectedField)
                  break
                }
              }
              if (fieldFound) break
            }
          }
          
          if (foundFields.length < rule.expectedFields.length) {
            fieldsValidation.found = false
            const missingFields = rule.expectedFields.filter(field => !foundFields.includes(field))
            fieldsValidation.details = `缺少字段: ${missingFields.join(', ')}`
          }
          
          // 5. 严格验证模式（如果启用）
          if (rule.strictValidation) {
            console.log('启用严格验证模式')
            const validationErrors: string[] = []
            
            // 验证行标题
            if (rule.expectedRowHeaders && rule.expectedRowHeaders.length > 0) {
              const foundRowHeaders: string[] = []
              for (const expectedHeader of rule.expectedRowHeaders) {
                let headerFound = false
                for (let i = 0; i < Math.min(values.length, 20); i++) {
                  for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                    const cellValue = values[i][j]
                    if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedHeader)) {
                      headerFound = true
                      foundRowHeaders.push(expectedHeader)
                      break
                    }
                  }
                  if (headerFound) break
                }
              }
              if (foundRowHeaders.length < rule.expectedRowHeaders.length) {
                const missingHeaders = rule.expectedRowHeaders.filter(header => !foundRowHeaders.includes(header))
                validationErrors.push(`缺少行标题: ${missingHeaders.join(', ')}`)
              }
            }
            
            // 验证列标题
            if (rule.expectedColumnHeaders && rule.expectedColumnHeaders.length > 0) {
              const foundColumnHeaders: string[] = []
              for (const expectedHeader of rule.expectedColumnHeaders) {
                let headerFound = false
                for (let i = 0; i < Math.min(values.length, 20); i++) {
                  for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                    const cellValue = values[i][j]
                    if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedHeader)) {
                      headerFound = true
                      foundColumnHeaders.push(expectedHeader)
                      break
                    }
                  }
                  if (headerFound) break
                }
              }
              if (foundColumnHeaders.length < rule.expectedColumnHeaders.length) {
                const missingHeaders = rule.expectedColumnHeaders.filter(header => !foundColumnHeaders.includes(header))
                validationErrors.push(`缺少列标题: ${missingHeaders.join(', ')}`)
              }
            }
            
            // 验证总计值
            if (rule.expectedTotalValue !== undefined) {
              let totalValueFound = false
              for (let i = 0; i < Math.min(values.length, 20); i++) {
                for (let j = 0; j < Math.min(values[i].length, 20); j++) {
                  const cellValue = values[i][j]
                  if (typeof cellValue === 'number' && Math.abs(cellValue - rule.expectedTotalValue) < 0.01) {
                    totalValueFound = true
                    break
                  }
                }
                if (totalValueFound) break
              }
              if (!totalValueFound) {
                validationErrors.push(`未找到期望的总计值: ${rule.expectedTotalValue}`)
              }
            }
            
            if (validationErrors.length > 0) {
              strictValidation.passed = false
              strictValidation.details = validationErrors.join('; ')
            }
          }
          
        } catch (e) {
          console.log('验证字段失败:', e)
        }
      }
      
      // 综合判断是否创建了透视表
      const pivotCreated = hasNewWorksheet || pivotElementFound || hasPivotStructure
      
      if (!pivotCreated) {
        return {
          success: false,
          message: '未检测到数据透视表。请按照操作步骤创建透视表：\n1. 选择数据范围A1:D6\n2. 右键点击选择"数据透视表"或通过"插入"菜单\n3. 确认数据范围并选择放置位置\n4. 点击"确定"创建透视表\n\n提示：透视表通常会创建新的工作表或在当前工作表中显示汇总数据',
          details: {
            expectedFields: rule.expectedFields,
            hasNewWorksheet,
            pivotElementFound,
            hasPivotStructure,
            foundSelector,
            hint: '请确保已经成功创建了数据透视表'
          }
        }
      }
      
      // 如果配置了字段验证但验证失败
      if (!fieldsValidation.found) {
        return {
          success: false,
          message: `透视表创建成功，但字段配置不完整。${fieldsValidation.details}\n\n请确保透视表包含以下字段：${rule.expectedFields?.join(', ')}`,
          details: {
            expectedFields: rule.expectedFields,
            pivotCreated: true,
            fieldsValidation,
            hint: '请检查透视表的字段配置'
          }
        }
      }
      
      // 如果启用了严格验证但验证失败
      if (rule.strictValidation && !strictValidation.passed) {
        return {
          success: false,
          message: `透视表创建成功，但不符合严格验证要求。${strictValidation.details}\n\n请确保透视表包含：\n- 行标题：${rule.expectedRowHeaders?.join(', ') || '无要求'}\n- 列标题：${rule.expectedColumnHeaders?.join(', ') || '无要求'}\n- 总计值：${rule.expectedTotalValue || '无要求'}`,
          details: {
            expectedFields: rule.expectedFields,
            expectedRowHeaders: rule.expectedRowHeaders,
            expectedColumnHeaders: rule.expectedColumnHeaders,
            expectedTotalValue: rule.expectedTotalValue,
            pivotCreated: true,
            fieldsValidation,
            strictValidation,
            hint: '请检查透视表的行标题、列标题和总计值是否正确'
          }
        }
      }
      
      return {
        success: true,
        message: rule.strictValidation ? 
          '数据透视表创建成功！所有验证项目均通过，任务完成。' : 
          '数据透视表创建成功！任务完成。',
        details: {
          expectedFields: rule.expectedFields,
          expectedRowHeaders: rule.expectedRowHeaders,
          expectedColumnHeaders: rule.expectedColumnHeaders,
          expectedTotalValue: rule.expectedTotalValue,
          pivotCreated: true,
          hasNewWorksheet,
          pivotElementFound,
          hasPivotStructure,
          foundSelector,
          fieldsValidation,
          strictValidation
        }
      }
      
    } catch (error) {
      console.error('透视表验证错误:', error)
      return {
        success: false,
        message: `透视表验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证筛选功能
   */
  private async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少数据范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.dataRange)

      // 获取数据范围的值
      const values = range.getValues()
      if (!values || values.length === 0) {
        return {
          success: false,
          message: '无法获取数据范围的值'
        }
      }

      console.log('筛选验证 - 获取到的原始数据:', values)

      // 检查数据范围是否包含表头
      const hasHeader = values.length > 0 && values[0] &&
        values[0].some((cell: unknown) => typeof cell === 'string' && isNaN(Number(cell)))

      console.log('筛选验证 - 是否有表头:', hasHeader)

      // 确定数据开始行
      const dataStartRow = hasHeader ? 1 : 0
      const originalDataRows = values.slice(dataStartRow)

      console.log('筛选验证 - 原始数据行:', originalDataRows)

      // 改进的筛选检测逻辑
      let actualVisibleData: unknown[][] = []
      let isFilterApplied = false

      // 方法1: 检查Univer API的筛选状态
      try {
        // 检查工作表是否有筛选器
        const hasAutoFilter = worksheet.hasFilter && worksheet.hasFilter()
        console.log('筛选验证 - 工作表是否有筛选器:', hasAutoFilter)

        if (hasAutoFilter) {
          isFilterApplied = true
          // 如果有筛选器，尝试获取可见行
          const visibleRows = worksheet.getVisibleRows && worksheet.getVisibleRows()
          if (visibleRows && visibleRows.length > 0) {
            actualVisibleData = visibleRows.slice(dataStartRow)
            console.log('筛选验证 - 从API获取的可见数据:', actualVisibleData)
          }
        }
      } catch (error) {
        console.log('筛选验证 - 无法通过API检测筛选状态:', error)
      }

      // 方法2: 通过DOM检测筛选状态
      if (!isFilterApplied) {
        try {
          // 检查是否有筛选按钮或下拉箭头
          const filterButtons = document.querySelectorAll('.univer-filter-button, .filter-dropdown, [data-filter], .auto-filter-button, .filter-icon')
          const hiddenRows = document.querySelectorAll('tr[style*="display: none"], .hidden-row, [data-hidden="true"]')

          console.log('筛选验证 - DOM检测结果:', {
            filterButtons: filterButtons.length,
            hiddenRows: hiddenRows.length
          })

          if (filterButtons.length > 0 || hiddenRows.length > 0) {
            isFilterApplied = true
            console.log('筛选验证 - 通过DOM检测到筛选已应用')
          }
        } catch (error) {
          console.log('筛选验证 - DOM检测失败:', error)
        }
      }

      // 方法3: 通过数据内容变化检测筛选
      if (!isFilterApplied && rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        // 检查当前可见的数据是否与期望的筛选结果匹配
        const expectedData = rule.expectedFilteredData
        let matchingRows: unknown[][] = []

        // 在原始数据中查找匹配期望筛选结果的行
        for (const expectedRow of expectedData) {
          for (const actualRow of originalDataRows) {
            let isRowMatch = true

            // 检查每一列是否匹配
            for (const key in expectedRow) {
              const colIndex = parseInt(key)
              const expectedValue = expectedRow[key]
              const actualValue = actualRow[colIndex]

              if (!this.compareValues(actualValue, expectedValue)) {
                isRowMatch = false
                break
              }
            }

            if (isRowMatch) {
              // 避免重复添加相同的行
              const isDuplicate = matchingRows.some(row =>
                row.every((cell, idx) => this.compareValues(cell, actualRow[idx]))
              )
              if (!isDuplicate) {
                matchingRows.push(actualRow)
              }
            }
          }
        }

        console.log('筛选验证 - 期望的筛选数据:', expectedData)
        console.log('筛选验证 - 在原始数据中找到的匹配行:', matchingRows)

        // 如果找到的匹配行数量与期望的筛选结果数量一致，且少于原始数据行数，说明可能已经应用了筛选
        if (matchingRows.length === expectedData.length && matchingRows.length < originalDataRows.length) {
          isFilterApplied = true
          actualVisibleData = matchingRows
          console.log('筛选验证 - 通过数据内容检测到筛选已应用')
        }
      }

      // 如果仍然没有检测到筛选，但有期望的筛选数据，说明用户没有执行筛选操作
      if (!isFilterApplied && rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        return {
          success: false,
          message: '未检测到筛选操作。请确保已正确执行以下步骤：\n\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件\n4. 确认筛选已生效（部分行被隐藏）',
          details: {
            filterDetected: false,
            expectedRows: rule.expectedVisibleRows,
            totalRows: originalDataRows.length,
            originalData: originalDataRows,
            expectedFilteredData: rule.expectedFilteredData,
            hint: '请检查是否已正确应用筛选功能'
          }
        }
      }

      // 如果没有应用筛选，使用所有原始数据
      if (!isFilterApplied) {
        actualVisibleData = originalDataRows
      }

      console.log('筛选验证 - 最终确定的可见数据:', actualVisibleData)

      // 验证可见行数
      if (rule.expectedVisibleRows !== undefined) {
        const actualRowCount = actualVisibleData.length
        console.log('筛选验证 - 行数检查:', { expected: rule.expectedVisibleRows, actual: actualRowCount })

        if (actualRowCount !== rule.expectedVisibleRows) {
          // 检查是否是因为没有应用筛选导致的
          if (actualRowCount === originalDataRows.length) {
            return {
              success: false,
              message: `未检测到筛选效果。期望显示 ${rule.expectedVisibleRows} 行数据，但当前显示了所有 ${actualRowCount} 行数据。\n\n请确保：\n1. 已选择数据范围并启用筛选功能\n2. 已设置正确的筛选条件\n3. 筛选条件已生效（部分数据被隐藏）`,
              details: {
                expectedRows: rule.expectedVisibleRows,
                actualRows: actualRowCount,
                originalRows: originalDataRows.length,
                hint: '请检查筛选操作是否正确完成'
              }
            }
          }

          return {
            success: false,
            message: `筛选后的行数不正确。期望: ${rule.expectedVisibleRows} 行，实际: ${actualRowCount} 行。请检查筛选条件设置。`,
            details: {
              expectedRows: rule.expectedVisibleRows,
              actualRows: actualRowCount,
              expectedData: rule.expectedFilteredData,
              actualData: actualVisibleData
            }
          }
        }
      }

      // 验证筛选后的数据内容
      if (rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        const expectedData = rule.expectedFilteredData

        // 检查是否找到了期望的数据
        if (actualVisibleData.length === 0) {
          return {
            success: false,
            message: '未找到符合筛选条件的数据。请检查筛选条件是否正确设置。',
            details: {
              expectedData: expectedData,
              actualData: actualVisibleData,
              originalData: originalDataRows
            }
          }
        }

        // 检查数据内容是否完全匹配
        if (actualVisibleData.length !== expectedData.length) {
          return {
            success: false,
            message: `筛选结果行数不匹配。期望: ${expectedData.length} 行，找到: ${actualVisibleData.length} 行。`,
            details: {
              expectedRows: expectedData.length,
              actualRows: actualVisibleData.length,
              expectedData: expectedData,
              actualData: actualVisibleData
            }
          }
        }

        // 验证每行数据的内容 - 改进匹配逻辑
        let allRowsMatch = true
        const mismatchDetails: string[] = []

        // 对于每个期望的行，在实际数据中查找匹配的行
        for (let i = 0; i < expectedData.length; i++) {
          const expectedRow = expectedData[i]
          let foundMatch = false

          // 在实际数据中查找匹配的行
          for (let j = 0; j < actualVisibleData.length; j++) {
            const actualRow = actualVisibleData[j]
            let rowMatches = true

            // 检查这一行的每一列是否匹配
            for (const key in expectedRow) {
              const colIndex = parseInt(key)
              const expectedValue = expectedRow[key]
              const actualValue = actualRow[colIndex]

              if (!this.compareValues(actualValue, expectedValue)) {
                rowMatches = false
                break
              }
            }

            if (rowMatches) {
              foundMatch = true
              break
            }
          }

          if (!foundMatch) {
            allRowsMatch = false
            const expectedRowStr = Object.entries(expectedRow).map(([col, val]) => `第${parseInt(col)+1}列:"${val}"`).join(', ')
            mismatchDetails.push(`期望的第${i+1}行数据未找到: ${expectedRowStr}`)
          }
        }

        if (!allRowsMatch) {
          return {
            success: false,
            message: `筛选后的数据内容不正确。${mismatchDetails.join('; ')}`,
            details: {
              expectedData: expectedData,
              actualData: actualVisibleData,
              mismatches: mismatchDetails
            }
          }
        }
      }

      return {
        success: true,
        message: '数据筛选验证通过！筛选结果符合预期。',
        details: {
          visibleRows: actualVisibleData.length,
          expectedRows: rule.expectedVisibleRows,
          actualData: actualVisibleData,
          dataRange: rule.dataRange
        }
      }

    } catch (error) {
      console.error('筛选验证错误:', error)
      return {
        success: false,
        message: `验证筛选时发生错误: ${error}`,
        details: { error: String(error) }
      }
    }
  }

  /**
   * 验证排序功能
   */
  private async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少数据范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.dataRange)
      
      // 获取数据范围的值
      const values = range.getValues()
      if (!values || values.length === 0) {
        return {
          success: false,
          message: '无法获取数据范围的值'
        }
      }

      // 验证排序顺序
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        const actualOrder: unknown[] = []
        
        // 提取指定列的数据（跳过表头）
        for (let i = 1; i < values.length; i++) {
          const row = values[i]
          if (row && row.length > 0) {
            // 如果指定了排序列，使用该列的数据，否则使用第一列
            const sortColumnIndex = rule.sortColumn ? this.getColumnIndex(rule.sortColumn) : 0
            actualOrder.push(row[sortColumnIndex])
          }
        }

        // 比较实际顺序和期望顺序
        let orderMatches = true
        if (actualOrder.length !== rule.expectedOrder.length) {
          orderMatches = false
        } else {
          for (let i = 0; i < rule.expectedOrder.length; i++) {
            if (!this.compareValues(actualOrder[i], rule.expectedOrder[i])) {
              orderMatches = false
              break
            }
          }
        }

        if (!orderMatches) {
          return {
            success: false,
            message: `排序顺序不正确。期望顺序: [${rule.expectedOrder.join(', ')}]，实际顺序: [${actualOrder.join(', ')}]`,
            details: {
              expectedOrder: rule.expectedOrder,
              actualOrder: actualOrder,
              sortColumn: rule.sortColumn,
              sortDirection: rule.sortDirection
            }
          }
        }
      }

      return {
        success: true,
        message: '数据排序验证通过！',
        details: {
          dataRange: rule.dataRange,
          sortColumn: rule.sortColumn,
          sortDirection: rule.sortDirection
        }
      }

    } catch (error) {
      return {
        success: false,
        message: `验证排序时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证条件格式功能
   */
  private async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.conditionRange && !rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少条件格式范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const targetRange = rule.conditionRange || rule.dataRange
      const range = worksheet.getRange(targetRange!)
      
      // 获取范围内的单元格数据
      const values = range.getValues()
      if (!values || values.length === 0) {
        return {
          success: false,
          message: '无法获取条件格式范围的值'
        }
      }

      // 检查条件格式是否已应用
      let hasConditionalFormat = false
      const formatDetails: Array<{
        cell: string;
        backgroundColor?: string;
        textColor?: string;
        value: unknown;
      }> = []

      // 遍历范围内的每个单元格
      for (let i = 0; i < values.length; i++) {
        for (let j = 0; j < values[i].length; j++) {
          const cellAddress = this.getCellAddress(i, j, targetRange!)
          const cellRange = worksheet.getRange(cellAddress)
          
          try {
            // 获取单元格样式信息
            const cellData = cellRange.getCellData()
            const cellStyle = cellData?.s || {}
            
            // 检查背景色
            if (cellStyle.bg && cellStyle.bg.rgb) {
              hasConditionalFormat = true
              formatDetails.push({
                cell: cellAddress,
                backgroundColor: cellStyle.bg.rgb,
                value: values[i][j]
              })
            }
            
            // 检查文字颜色
            if (cellStyle.cl && cellStyle.cl.rgb) {
              hasConditionalFormat = true
              formatDetails.push({
                cell: cellAddress,
                textColor: cellStyle.cl.rgb,
                value: values[i][j]
              })
            }
          } catch (e) {
            // 忽略单个单元格的错误
            console.log(`检查单元格 ${cellAddress} 样式时出错:`, e)
          }
        }
      }

      if (!hasConditionalFormat) {
        return {
          success: false,
          message: '未检测到条件格式。请确保已正确设置条件格式规则。',
          details: {
            conditionRange: targetRange,
            expectedBackgroundColor: rule.expectedBackgroundColor,
            expectedTextColor: rule.expectedTextColor
          }
        }
      }

      // 如果指定了期望的颜色，进行验证
      if (rule.expectedBackgroundColor || rule.expectedTextColor) {
        let colorMatches = false
        
        for (const detail of formatDetails) {
          if (rule.expectedBackgroundColor && detail.backgroundColor) {
            if (this.compareColors(detail.backgroundColor, rule.expectedBackgroundColor)) {
              colorMatches = true
              break
            }
          }
          if (rule.expectedTextColor && detail.textColor) {
            if (this.compareColors(detail.textColor, rule.expectedTextColor)) {
              colorMatches = true
              break
            }
          }
        }

        if (!colorMatches) {
          return {
            success: false,
            message: '条件格式颜色不正确。请检查条件格式规则设置。',
            details: {
              expectedBackgroundColor: rule.expectedBackgroundColor,
              expectedTextColor: rule.expectedTextColor,
              actualFormats: formatDetails
            }
          }
        }
      }

      return {
        success: true,
        message: '条件格式验证通过！',
        details: {
          conditionRange: targetRange,
          formatDetails: formatDetails
        }
      }

    } catch (error) {
      return {
        success: false,
        message: `验证条件格式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  // 辅助方法
  private compareValues(actual: unknown, expected: unknown): boolean {
    // 处理数字比较
    if (typeof expected === 'number' && typeof actual === 'number') {
      return Math.abs(actual - expected) < 0.0001 // 浮点数比较
    }
    
    // 处理字符串比较（忽略大小写和前后空格）
    if (typeof expected === 'string' && typeof actual === 'string') {
      return actual.trim().toLowerCase() === expected.trim().toLowerCase()
    }
    
    // 其他类型直接比较
    return actual === expected
  }

  /**
   * 获取列索引（A=0, B=1, C=2...）
   */
  private getColumnIndex(column: string): number {
    let result = 0
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
    }
    return result - 1
  }

  /**
   * 根据行列索引和范围获取单元格地址
   */
  private getCellAddress(rowIndex: number, colIndex: number, range: string): string {
    // 解析范围起始位置
    const rangeMatch = range.match(/([A-Z]+)(\d+)/)
    if (!rangeMatch) {
      // 如果无法解析范围，使用默认计算
      const colLetter = this.getColumnLetter(colIndex)
      return `${colLetter}${rowIndex + 1}`
    }
    
    const startCol = rangeMatch[1]
    const startRow = parseInt(rangeMatch[2])
    const startColIndex = this.getColumnIndex(startCol)
    
    const targetColIndex = startColIndex + colIndex
    const targetRow = startRow + rowIndex
    const targetColLetter = this.getColumnLetter(targetColIndex)
    
    return `${targetColLetter}${targetRow}`
  }

  /**
   * 将列索引转换为列字母（0=A, 1=B, 2=C...）
   */
  private getColumnLetter(index: number): string {
    let result = ''
    while (index >= 0) {
      result = String.fromCharCode((index % 26) + 'A'.charCodeAt(0)) + result
      index = Math.floor(index / 26) - 1
    }
    return result
  }

  /**
   * 比较颜色值
   */
  private compareColors(actual: string, expected: string): boolean {
    // 标准化颜色值（移除#号，转换为大写）
    const normalizeColor = (color: string) => {
      return color.replace('#', '').toUpperCase()
    }
    
    const actualNormalized = normalizeColor(actual)
    const expectedNormalized = normalizeColor(expected)
    
    return actualNormalized === expectedNormalized
  }

  private isCurrencyFormat(format: string): boolean {
    return format.includes('¥') || format.includes('$') || format.includes('€') || 
           format.includes('currency') || format.includes('CURRENCY')
  }

  private isPercentageFormat(format: string): boolean {
    return format.includes('%')
  }

  private isDateFormat(format: string): boolean {
    return format.includes('yyyy') || format.includes('mm') || format.includes('dd') ||
           format.includes('YYYY') || format.includes('MM') || format.includes('DD')
  }
}

/**
 * 创建验证服务实例
 * @param univerAPI Univer API实例
 * @returns 验证服务实例
 */
export function createValidationService(univerAPI: UniverAPI): ExcelValidationService {
  return new ExcelValidationService(univerAPI)
}

/**
 * 验证任务的便捷函数
 * @param univerAPI Univer API实例
 * @param validationRule 验证规则
 * @returns 验证结果
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  const service = createValidationService(univerAPI)
  return await service.validateTask(validationRule)
}